#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电商订单数据多维度分析报告生成器
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.offline as pyo
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PostingsAnalyzer:
    def __init__(self, csv_file):
        """初始化分析器"""
        self.csv_file = csv_file
        self.df = None
        self.load_data()
        
    def load_data(self):
        """加载和预处理数据"""
        try:
            # 读取CSV文件，使用分号作为分隔符
            self.df = pd.read_csv(self.csv_file, sep=';', encoding='utf-8')
            
            # 数据清洗和类型转换
            self.preprocess_data()
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            
    def preprocess_data(self):
        """数据预处理"""
        if self.df is None:
            return
            
        # 移除空行
        self.df = self.df.dropna(how='all')
        
        # 转换日期列
        date_columns = ['正在处理中', '发运日期', '配送日期', '实际转移配送日期', '取消日期']
        for col in date_columns:
            if col in self.df.columns:
                self.df[col] = pd.to_datetime(self.df[col], errors='coerce')
        
        # 转换数值列
        numeric_columns = ['发货的金额', '商品的总价', '对买家而言的商品价格', '数量', '运费', 
                          '商品打折前的价格', '折扣，卢布']
        for col in numeric_columns:
            if col in self.df.columns:
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        
        # 提取折扣百分比
        if '折扣 %' in self.df.columns:
            self.df['折扣百分比'] = self.df['折扣 %'].str.replace('%', '').astype(float, errors='ignore')
        
        # 计算配送时长
        if '发运日期' in self.df.columns and '配送日期' in self.df.columns:
            self.df['配送时长'] = (self.df['配送日期'] - self.df['发运日期']).dt.days
            
    def generate_html_report(self):
        """生成HTML分析报告"""
        if self.df is None:
            return "数据加载失败，无法生成报告"
            
        # 创建各种分析图表
        charts = self.create_all_charts()
        
        # 生成统计摘要
        summary = self.generate_summary()
        
        # 创建HTML模板
        html_content = self.create_html_template(summary, charts)
        
        return html_content
    
    def generate_summary(self):
        """生成数据摘要统计"""
        summary = {}
        
        # 基本统计
        summary['总订单数'] = len(self.df)
        summary['总销售额_CNY'] = self.df['发货的金额'].sum()
        summary['总销售额_RUB'] = self.df['对买家而言的商品价格'].sum()
        summary['平均订单金额_CNY'] = self.df['发货的金额'].mean()
        summary['平均订单金额_RUB'] = self.df['对买家而言的商品价格'].mean()
        
        # 时间范围
        if '发运日期' in self.df.columns:
            summary['最早发运日期'] = self.df['发运日期'].min()
            summary['最晚发运日期'] = self.df['发运日期'].max()
        
        # 配送统计
        if '配送时长' in self.df.columns:
            summary['平均配送时长'] = self.df['配送时长'].mean()
            summary['最短配送时长'] = self.df['配送时长'].min()
            summary['最长配送时长'] = self.df['配送时长'].max()
        
        # 商品统计
        summary['商品种类数'] = self.df['商品名称'].nunique()
        summary['最受欢迎商品'] = self.df['商品名称'].value_counts().index[0]
        
        # 折扣统计
        if '折扣百分比' in self.df.columns:
            summary['平均折扣率'] = self.df['折扣百分比'].mean()
            summary['最大折扣率'] = self.df['折扣百分比'].max()
        
        return summary
    
    def create_all_charts(self):
        """创建所有分析图表"""
        charts = {}
        
        # 1. 销售趋势分析
        charts['sales_trend'] = self.create_sales_trend_chart()
        
        # 2. 商品分析
        charts['product_analysis'] = self.create_product_analysis_chart()
        
        # 3. 价格分布分析
        charts['price_distribution'] = self.create_price_distribution_chart()
        
        # 4. 配送分析
        charts['delivery_analysis'] = self.create_delivery_analysis_chart()
        
        # 5. 折扣分析
        charts['discount_analysis'] = self.create_discount_analysis_chart()
        
        # 6. 支付方式分析
        charts['payment_analysis'] = self.create_payment_analysis_chart()
        
        # 7. 地理分布分析
        charts['geo_analysis'] = self.create_geo_analysis_chart()
        
        return charts

    def create_sales_trend_chart(self):
        """创建销售趋势图表"""
        if '发运日期' not in self.df.columns:
            return "<p>缺少发运日期数据</p>"

        # 按日期聚合销售数据
        daily_sales = self.df.groupby(self.df['发运日期'].dt.date).agg({
            '发货的金额': 'sum',
            '对买家而言的商品价格': 'sum',
            '订单号': 'count'
        }).reset_index()

        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('每日销售额(CNY)', '每日销售额(RUB)', '每日订单数', '累计销售趋势'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )

        # 每日销售额CNY
        fig.add_trace(
            go.Scatter(x=daily_sales['发运日期'], y=daily_sales['发货的金额'],
                      mode='lines+markers', name='CNY销售额'),
            row=1, col=1
        )

        # 每日销售额RUB
        fig.add_trace(
            go.Scatter(x=daily_sales['发运日期'], y=daily_sales['对买家而言的商品价格'],
                      mode='lines+markers', name='RUB销售额'),
            row=1, col=2
        )

        # 每日订单数
        fig.add_trace(
            go.Bar(x=daily_sales['发运日期'], y=daily_sales['订单号'],
                   name='订单数'),
            row=2, col=1
        )

        # 累计销售趋势
        daily_sales['累计销售额'] = daily_sales['发货的金额'].cumsum()
        fig.add_trace(
            go.Scatter(x=daily_sales['发运日期'], y=daily_sales['累计销售额'],
                      mode='lines', name='累计销售额'),
            row=2, col=2
        )

        fig.update_layout(height=800, title_text="销售趋势分析")
        return fig.to_html(include_plotlyjs='cdn')

    def create_product_analysis_chart(self):
        """创建商品分析图表"""
        # 商品销量统计
        product_sales = self.df.groupby('商品名称').agg({
            '数量': 'sum',
            '发货的金额': 'sum',
            '订单号': 'count'
        }).reset_index()
        product_sales = product_sales.sort_values('数量', ascending=False).head(10)

        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('热销商品TOP10', '商品销售额分布', '商品尺寸分析', '商品颜色分析'),
            specs=[[{"type": "bar"}, {"type": "pie"}],
                   [{"type": "bar"}, {"type": "pie"}]]
        )

        # 热销商品TOP10
        fig.add_trace(
            go.Bar(x=product_sales['商品名称'], y=product_sales['数量'],
                   name='销量'),
            row=1, col=1
        )

        # 商品销售额分布
        fig.add_trace(
            go.Pie(labels=product_sales['商品名称'], values=product_sales['发货的金额'],
                   name="销售额分布"),
            row=1, col=2
        )

        # 提取尺寸信息
        size_pattern = self.df['货号'].str.extract(r'(\d+\.?\d*-\d+\.?\d*)')
        size_counts = size_pattern[0].value_counts().head(8)

        fig.add_trace(
            go.Bar(x=size_counts.index, y=size_counts.values,
                   name='尺寸销量'),
            row=2, col=1
        )

        # 提取颜色信息
        color_pattern = self.df['货号'].str.split('-').str[-1]
        color_counts = color_pattern.value_counts().head(8)

        fig.add_trace(
            go.Pie(labels=color_counts.index, values=color_counts.values,
                   name="颜色分布"),
            row=2, col=2
        )

        fig.update_layout(height=800, title_text="商品分析")
        return fig.to_html(include_plotlyjs='cdn')

    def create_price_distribution_chart(self):
        """创建价格分布分析图表"""
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('CNY价格分布', 'RUB价格分布', '价格vs销量关系', '折扣分布'),
            specs=[[{"type": "histogram"}, {"type": "histogram"}],
                   [{"type": "scatter"}, {"type": "box"}]]
        )

        # CNY价格分布
        fig.add_trace(
            go.Histogram(x=self.df['发货的金额'], nbinsx=20, name='CNY价格'),
            row=1, col=1
        )

        # RUB价格分布
        fig.add_trace(
            go.Histogram(x=self.df['对买家而言的商品价格'], nbinsx=20, name='RUB价格'),
            row=1, col=2
        )

        # 价格vs销量关系
        fig.add_trace(
            go.Scatter(x=self.df['发货的金额'], y=self.df['数量'],
                      mode='markers', name='价格-销量'),
            row=2, col=1
        )

        # 折扣分布
        if '折扣百分比' in self.df.columns:
            fig.add_trace(
                go.Box(y=self.df['折扣百分比'], name='折扣分布'),
                row=2, col=2
            )

        fig.update_layout(height=800, title_text="价格分布分析")
        return fig.to_html(include_plotlyjs='cdn')

    def create_delivery_analysis_chart(self):
        """创建配送分析图表"""
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('配送时长分布', '配送方式统计', '配送集群分析', '配送时长趋势'),
            specs=[[{"type": "histogram"}, {"type": "pie"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )

        # 配送时长分布
        if '配送时长' in self.df.columns:
            fig.add_trace(
                go.Histogram(x=self.df['配送时长'], nbinsx=15, name='配送时长'),
                row=1, col=1
            )

        # 配送方式统计
        if '配送方式' in self.df.columns:
            delivery_counts = self.df['配送方式'].value_counts()
            fig.add_trace(
                go.Pie(labels=delivery_counts.index, values=delivery_counts.values,
                       name="配送方式"),
                row=1, col=2
            )

        # 配送集群分析
        if '配送集群' in self.df.columns:
            cluster_counts = self.df['配送集群'].value_counts().head(10)
            fig.add_trace(
                go.Bar(x=cluster_counts.index, y=cluster_counts.values,
                       name='配送集群'),
                row=2, col=1
            )

        # 配送时长趋势
        if '发运日期' in self.df.columns and '配送时长' in self.df.columns:
            delivery_trend = self.df.groupby(self.df['发运日期'].dt.date)['配送时长'].mean().reset_index()
            fig.add_trace(
                go.Scatter(x=delivery_trend['发运日期'], y=delivery_trend['配送时长'],
                          mode='lines+markers', name='平均配送时长'),
                row=2, col=2
            )

        fig.update_layout(height=800, title_text="配送分析")
        return fig.to_html(include_plotlyjs='cdn')

    def create_discount_analysis_chart(self):
        """创建折扣分析图表"""
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('折扣率分布', '促销活动效果', '折扣vs销售额', '折扣趋势'),
            specs=[[{"type": "histogram"}, {"type": "bar"}],
                   [{"type": "scatter"}, {"type": "scatter"}]]
        )

        # 折扣率分布
        if '折扣百分比' in self.df.columns:
            fig.add_trace(
                go.Histogram(x=self.df['折扣百分比'], nbinsx=20, name='折扣率'),
                row=1, col=1
            )

        # 促销活动效果
        if '促销活动' in self.df.columns:
            promo_sales = self.df.groupby('促销活动')['发货的金额'].sum().sort_values(ascending=False).head(8)
            fig.add_trace(
                go.Bar(x=promo_sales.index, y=promo_sales.values,
                       name='促销销售额'),
                row=1, col=2
            )

        # 折扣vs销售额
        if '折扣百分比' in self.df.columns:
            fig.add_trace(
                go.Scatter(x=self.df['折扣百分比'], y=self.df['发货的金额'],
                          mode='markers', name='折扣-销售额'),
                row=2, col=1
            )

        # 折扣趋势
        if '发运日期' in self.df.columns and '折扣百分比' in self.df.columns:
            discount_trend = self.df.groupby(self.df['发运日期'].dt.date)['折扣百分比'].mean().reset_index()
            fig.add_trace(
                go.Scatter(x=discount_trend['发运日期'], y=discount_trend['折扣百分比'],
                          mode='lines+markers', name='平均折扣率'),
                row=2, col=2
            )

        fig.update_layout(height=800, title_text="折扣分析")
        return fig.to_html(include_plotlyjs='cdn')

    def create_payment_analysis_chart(self):
        """创建支付方式分析图表"""
        if '付款方式' not in self.df.columns:
            return "<p>缺少付款方式数据</p>"

        payment_stats = self.df.groupby('付款方式').agg({
            '订单号': 'count',
            '发货的金额': 'sum',
            '对买家而言的商品价格': 'mean'
        }).reset_index()

        fig = make_subplots(
            rows=1, cols=3,
            subplot_titles=('支付方式订单数', '支付方式销售额', '支付方式平均订单价值'),
            specs=[[{"type": "pie"}, {"type": "bar"}, {"type": "bar"}]]
        )

        # 支付方式订单数
        fig.add_trace(
            go.Pie(labels=payment_stats['付款方式'], values=payment_stats['订单号'],
                   name="订单数分布"),
            row=1, col=1
        )

        # 支付方式销售额
        fig.add_trace(
            go.Bar(x=payment_stats['付款方式'], y=payment_stats['发货的金额'],
                   name='销售额'),
            row=1, col=2
        )

        # 支付方式平均订单价值
        fig.add_trace(
            go.Bar(x=payment_stats['付款方式'], y=payment_stats['对买家而言的商品价格'],
                   name='平均订单价值'),
            row=1, col=3
        )

        fig.update_layout(height=500, title_text="支付方式分析")
        return fig.to_html(include_plotlyjs='cdn')

    def create_geo_analysis_chart(self):
        """创建地理分布分析图表"""
        if '配送地址' not in self.df.columns:
            return "<p>缺少配送地址数据</p>"

        # 提取地区信息
        regions = []
        for addr in self.df['配送地址'].dropna():
            if ',' in str(addr):
                parts = str(addr).split(',')
                if len(parts) >= 2:
                    regions.append(parts[1].strip())

        if not regions:
            return "<p>无法解析地区信息</p>"

        region_counts = pd.Series(regions).value_counts().head(10)

        fig = go.Figure(data=[
            go.Bar(x=region_counts.index, y=region_counts.values)
        ])

        fig.update_layout(
            title="配送地区分布TOP10",
            xaxis_title="地区",
            yaxis_title="订单数",
            height=500
        )

        return fig.to_html(include_plotlyjs='cdn')

    def create_html_template(self, summary, charts):
        """创建HTML报告模板"""
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商订单数据多维度分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }}
        .header h1 {{
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }}
        .header p {{
            color: #666;
            font-size: 1.1em;
        }}
        .summary {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 40px;
        }}
        .summary h2 {{
            margin-top: 0;
            font-size: 1.8em;
            margin-bottom: 20px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        .summary-item {{
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }}
        .summary-item h3 {{
            margin: 0 0 10px 0;
            font-size: 1.1em;
            opacity: 0.9;
        }}
        .summary-item p {{
            margin: 0;
            font-size: 1.3em;
            font-weight: bold;
        }}
        .section {{
            margin-bottom: 50px;
        }}
        .section h2 {{
            color: #333;
            border-left: 5px solid #007bff;
            padding-left: 15px;
            margin-bottom: 25px;
            font-size: 1.8em;
        }}
        .chart-container {{
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }}
        .footer {{
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
        }}
        .toc {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }}
        .toc h3 {{
            margin-top: 0;
            color: #333;
        }}
        .toc ul {{
            list-style-type: none;
            padding-left: 0;
        }}
        .toc li {{
            margin: 8px 0;
        }}
        .toc a {{
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }}
        .toc a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 电商订单数据多维度分析报告</h1>
            <p>基于 postings.csv 数据的全面分析 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#summary">📈 数据概览</a></li>
                <li><a href="#sales">💰 销售趋势分析</a></li>
                <li><a href="#products">🛍️ 商品分析</a></li>
                <li><a href="#pricing">💵 价格分布分析</a></li>
                <li><a href="#delivery">🚚 配送分析</a></li>
                <li><a href="#discount">🎯 折扣分析</a></li>
                <li><a href="#payment">💳 支付方式分析</a></li>
                <li><a href="#geography">🌍 地理分布分析</a></li>
            </ul>
        </div>

        <div id="summary" class="summary">
            <h2>📈 数据概览</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <h3>总订单数</h3>
                    <p>{summary.get('总订单数', 'N/A')}</p>
                </div>
                <div class="summary-item">
                    <h3>总销售额 (CNY)</h3>
                    <p>¥{summary.get('总销售额_CNY', 0):,.2f}</p>
                </div>
                <div class="summary-item">
                    <h3>总销售额 (RUB)</h3>
                    <p>₽{summary.get('总销售额_RUB', 0):,.2f}</p>
                </div>
                <div class="summary-item">
                    <h3>平均订单金额 (CNY)</h3>
                    <p>¥{summary.get('平均订单金额_CNY', 0):,.2f}</p>
                </div>
                <div class="summary-item">
                    <h3>平均订单金额 (RUB)</h3>
                    <p>₽{summary.get('平均订单金额_RUB', 0):,.2f}</p>
                </div>
                <div class="summary-item">
                    <h3>商品种类数</h3>
                    <p>{summary.get('商品种类数', 'N/A')}</p>
                </div>
                <div class="summary-item">
                    <h3>平均配送时长</h3>
                    <p>{summary.get('平均配送时长', 0):.1f} 天</p>
                </div>
                <div class="summary-item">
                    <h3>平均折扣率</h3>
                    <p>{summary.get('平均折扣率', 0):.1f}%</p>
                </div>
            </div>
        </div>"""

        # 添加各个分析部分
        sections = [
            ('sales', '💰 销售趋势分析', charts.get('sales_trend', '')),
            ('products', '🛍️ 商品分析', charts.get('product_analysis', '')),
            ('pricing', '💵 价格分布分析', charts.get('price_distribution', '')),
            ('delivery', '🚚 配送分析', charts.get('delivery_analysis', '')),
            ('discount', '🎯 折扣分析', charts.get('discount_analysis', '')),
            ('payment', '💳 支付方式分析', charts.get('payment_analysis', '')),
            ('geography', '🌍 地理分布分析', charts.get('geo_analysis', ''))
        ]

        for section_id, title, content in sections:
            html_template += f"""
        <div id="{section_id}" class="section">
            <h2>{title}</h2>
            <div class="chart-container">
                {content}
            </div>
        </div>"""

        html_template += """
        <div class="footer">
            <p>📊 报告由 Python 数据分析工具自动生成 | 数据来源: postings.csv</p>
            <p>🔧 技术栈: Pandas, Plotly, HTML/CSS</p>
        </div>
    </div>
</body>
</html>"""

        return html_template


def main():
    """主函数"""
    print("🚀 开始生成电商订单数据分析报告...")

    # 创建分析器实例
    analyzer = PostingsAnalyzer('postings.csv')

    if analyzer.df is None:
        print("❌ 数据加载失败，请检查 postings.csv 文件")
        return

    print(f"✅ 成功加载 {len(analyzer.df)} 条订单数据")

    # 生成HTML报告
    print("📊 正在生成分析图表...")
    html_report = analyzer.generate_html_report()

    # 保存报告
    output_file = 'postings_analysis_report.html'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_report)

    print(f"✅ 分析报告已生成: {output_file}")
    print("🌐 请在浏览器中打开该文件查看完整报告")


if __name__ == "__main__":
    main()
